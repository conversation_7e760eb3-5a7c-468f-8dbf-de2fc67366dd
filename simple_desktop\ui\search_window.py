"""
独立浮动搜索窗口
基于PRD要求实现的独立浮动窗口，可以被其他应用覆盖和移动
"""

import os
from typing import List, Optional
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLineEdit, QListWidget,
    QListWidgetItem, QPushButton, QTabBar, QLabel, QApplication
)
from PySide6.QtCore import Qt, QTimer, Signal, QThread, QMutex, QPropertyAnimation, QEasingCurve, QPoint
from PySide6.QtGui import QFont, QDesktopServices, QIcon, QShortcut, QKeySequence, QMouseEvent
from PySide6.QtCore import QUrl

from ..search.engine import FileSearchEngine, SearchResult
from ..core.config import config_manager
from ..core.profile_manager import profile_manager


class DragHandle(QWidget):
    """专用拖拽手柄"""

    def __init__(self, parent_window=None):
        super().__init__()
        self.parent_window = parent_window
        self.drag_start_position = QPoint()
        self.dragging = False

        # 设置固定尺寸：宽度自适应，高度为12px（关闭按钮的一半）
        self.setFixedHeight(12)
        self.setMinimumWidth(300)  # 增加最小宽度以确保足够的拖拽区域

        # 设置样式 - 与关闭按钮保持完全一致的视觉风格，使用最高优先级
        self.setStyleSheet("""
            DragHandle {
                background-color: white !important;
                background: white !important;
                border: 1px solid rgba(200, 200, 200, 0.8) !important;
                border-radius: 6px !important;
                margin: 0px !important;
                min-height: 12px !important;
                max-height: 12px !important;
                padding: 0px !important;
            }
            DragHandle:hover {
                background-color: #f8f8f8 !important;
                background: #f8f8f8 !important;
                border-color: rgba(160, 160, 160, 0.9) !important;
            }
        """)

        # 设置鼠标光标
        self.setCursor(Qt.CursorShape.SizeAllCursor)

        # 强制设置背景色（通过代码确保背景显示）
        self.setAutoFillBackground(True)
        from PySide6.QtGui import QPalette, QColor
        palette = self.palette()
        white_color = QColor(255, 255, 255, 255)  # 完全不透明的白色
        palette.setColor(QPalette.ColorRole.Window, white_color)
        palette.setColor(QPalette.ColorRole.Base, white_color)
        palette.setColor(QPalette.ColorRole.Background, white_color)
        self.setPalette(palette)

        # 额外的背景设置
        self.setAttribute(Qt.WidgetAttribute.WA_StyledBackground, True)

        # 添加拖拽指示器（三条平行线）
        self.setup_drag_indicator()

    def setup_drag_indicator(self):
        """设置拖拽指示器 - 在圆角长方形中合理布局"""
        layout = QHBoxLayout()
        layout.setContentsMargins(8, 0, 8, 0)  # 适中的左右边距
        layout.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
        layout.setSpacing(6)  # 图标和文字之间的间距

        # 创建拖拽指示器标签 - 使用更明显的图标和深色
        indicator = QLabel("≡")  # 使用三条水平线作为拖拽指示器
        indicator.setStyleSheet("""
            QLabel {
                color: #333333;
                font-size: 12px;
                font-weight: bold;
                background-color: transparent;
                border: none;
                padding: 0px 4px;
            }
        """)
        indicator.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # 添加文本提示 - 使用更深的颜色和更粗的字体
        text_label = QLabel("拖拽移动窗口")
        text_label.setStyleSheet("""
            QLabel {
                color: #555555;
                font-size: 9px;
                background-color: transparent;
                border: none;
                font-family: 'Microsoft YaHei';
                font-weight: 600;
            }
        """)

        layout.addWidget(indicator)
        layout.addWidget(text_label)
        layout.addStretch()  # 推送到左侧，让右侧留出空间

        self.setLayout(layout)

    def mousePressEvent(self, event: QMouseEvent):
        """鼠标按下事件"""
        if event.button() == Qt.MouseButton.LeftButton and self.parent_window:
            self.drag_start_position = event.globalPosition().toPoint() - self.parent_window.frameGeometry().topLeft()
            self.dragging = True
            event.accept()

    def mouseMoveEvent(self, event: QMouseEvent):
        """鼠标移动事件"""
        if self.dragging and self.parent_window and event.buttons() == Qt.MouseButton.LeftButton:
            new_position = event.globalPosition().toPoint() - self.drag_start_position
            self.parent_window.move(new_position)
            event.accept()

    def mouseReleaseEvent(self, event: QMouseEvent):
        """鼠标释放事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.dragging = False
            event.accept()

    def paintEvent(self, event):
        """自定义绘制事件，确保背景正确显示"""
        from PySide6.QtGui import QPainter, QBrush, QColor, QPen
        from PySide6.QtCore import QRect

        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # 绘制白色背景
        rect = self.rect()
        brush = QBrush(QColor(255, 255, 255, 255))  # 纯白色
        painter.setBrush(brush)

        # 绘制边框
        pen = QPen(QColor(200, 200, 200, 204))  # rgba(200, 200, 200, 0.8)
        pen.setWidth(1)
        painter.setPen(pen)

        # 绘制圆角矩形
        painter.drawRoundedRect(rect, 6, 6)

        # 调用父类的paintEvent来绘制子组件
        super().paintEvent(event)


class DragBar(QWidget):
    """标题栏容器"""

    close_clicked = Signal()

    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_window = parent

        self.setFixedHeight(30)
        self.setup_ui()

    def setup_ui(self):
        """设置UI"""
        layout = QHBoxLayout()
        layout.setContentsMargins(8, 3, 8, 3)  # 上下留出3px边距让拖拽手柄居中
        layout.setSpacing(10)  # 在拖拽手柄和关闭按钮之间保留10px间距

        # 左侧专用拖拽手柄 - 扩展到占据大部分空间
        self.drag_handle = DragHandle(self.parent_window)
        layout.addWidget(self.drag_handle, 1)  # 使用stretch factor让其占据最大可用空间

        # 右侧关闭按钮
        self.close_button = QPushButton("✕")
        self.close_button.setFixedSize(24, 24)
        self.close_button.setStyleSheet("""
            QPushButton {
                background-color: #ffffff;
                border: 1px solid rgba(200, 200, 200, 0.8);
                border-radius: 12px;
                color: #d32f2f;
                font-size: 14px;
                font-weight: bold;
                padding: 0px;
            }
            QPushButton:hover {
                background-color: #f8f8f8;
                border-color: rgba(160, 160, 160, 0.9);
                color: #b71c1c;
            }
            QPushButton:pressed {
                background-color: rgba(211, 47, 47, 0.1);
                border-color: rgba(211, 47, 47, 1.0);
            }
        """)
        self.close_button.clicked.connect(self.close_clicked.emit)
        layout.addWidget(self.close_button, 0)  # 不使用stretch，保持固定大小

        self.setLayout(layout)

        # 设置整个拖拽栏的样式 - 使用透明背景避免与DragHandle冲突
        self.setStyleSheet("""
            DragBar {
                background-color: transparent;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                border-bottom: 1px solid rgba(220, 220, 220, 0.7);
            }
        """)


class SearchWorker(QThread):
    """搜索工作线程"""
    
    results_ready = Signal(list)
    
    def __init__(self, search_engine: FileSearchEngine):
        super().__init__()
        self.search_engine = search_engine
        self.query = ""
        self.mutex = QMutex()
    
    def set_query(self, query: str):
        """设置搜索查询"""
        self.mutex.lock()
        self.query = query
        self.mutex.unlock()
    
    def run(self):
        """执行搜索"""
        self.mutex.lock()
        current_query = self.query
        self.mutex.unlock()
        
        if not current_query.strip():
            self.results_ready.emit([])
            return
        
        try:
            results = self.search_engine.search(current_query, limit=50)
            self.results_ready.emit(results)
        except Exception as e:
            print(f"搜索错误: {e}")
            self.results_ready.emit([])


class FloatingSearchWindow(QWidget):
    """独立浮动搜索窗口"""
    
    # 信号定义
    window_hidden = Signal()
    profile_changed = Signal(int)
    
    def __init__(self, app_manager=None):
        super().__init__()
        self.app_manager = app_manager
        
        # 初始化Profile和搜索引擎
        self.current_profile_id = profile_manager.current_profile_id
        self.search_engine = FileSearchEngine(profile_id=self.current_profile_id)
        self.search_worker = SearchWorker(self.search_engine)
        self.search_worker.results_ready.connect(self.on_search_results)
        
        # 搜索延迟定时器
        self.search_timer = QTimer()
        self.search_timer.setSingleShot(True)
        self.search_timer.timeout.connect(self.perform_search)
        
        # 当前默认行为
        self.current_action = config_manager.get_default_action()
        
        # 文件类型过滤
        self.enabled_file_types = []
        
        # 初始化UI
        self.init_ui()
        self.setup_window_properties()
        self.setup_animations()

        # 设置GUI内热键
        self.setup_gui_shortcuts()

        # 连接Profile管理器
        profile_manager.add_profile_switch_callback(self.on_profile_switched)
    
    def init_ui(self):
        """初始化用户界面"""
        # 创建主布局
        self.main_layout = QVBoxLayout()
        self.main_layout.setContentsMargins(0, 0, 0, 16)  # 顶部边距为0，为拖拽栏让出空间
        self.main_layout.setSpacing(12)

        # 创建拖拽栏
        self.create_drag_bar()

        # 创建内容区域布局
        self.content_layout = QVBoxLayout()
        self.content_layout.setContentsMargins(16, 8, 16, 0)  # 内容区域的边距
        self.content_layout.setSpacing(12)

        # 创建Profile标签栏
        self.create_profile_tabs()

        # 创建搜索输入区域
        self.create_search_input()

        # 创建控制按钮区域
        self.create_control_buttons()

        # 创建结果列表
        self.create_results_list()

        # 将内容布局添加到主布局
        self.main_layout.addLayout(self.content_layout)

        self.setLayout(self.main_layout)

    def create_drag_bar(self):
        """创建拖拽栏"""
        self.drag_bar = DragBar(self)
        self.drag_bar.close_clicked.connect(self.on_close_button_clicked)
        self.main_layout.addWidget(self.drag_bar)

    def on_close_button_clicked(self):
        """处理关闭按钮点击"""
        if self.app_manager:
            print("[DRAG-BAR] 关闭按钮点击，退出应用程序")
            self.app_manager.quit_app()
        else:
            print("[DRAG-BAR] 关闭按钮点击，退出应用程序")
            QApplication.instance().quit()

    def create_profile_tabs(self):
        """创建Profile标签栏"""
        self.tab_bar = QTabBar()
        self.tab_bar.setShape(QTabBar.Shape.RoundedNorth)
        self.tab_bar.setExpanding(False)
        self.tab_bar.setMovable(False)
        
        # 添加10个标签（0-9）
        for i in range(10):
            tab_name = profile_manager.get_profile_name(i)
            self.tab_bar.addTab(tab_name)
        
        # 设置当前选中的标签
        self.tab_bar.setCurrentIndex(self.current_profile_id)
        
        # 连接标签切换信号
        self.tab_bar.currentChanged.connect(self.on_tab_changed)
        
        # 设置标签栏样式
        self.tab_bar.setFixedHeight(32)
        self.tab_bar.setStyleSheet(self.get_tab_style())

        self.content_layout.addWidget(self.tab_bar)
    
    def create_search_input(self):
        """创建搜索输入区域"""
        input_layout = QHBoxLayout()
        input_layout.setSpacing(8)
        
        # 主搜索输入框
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("输入文件名进行搜索...")
        self.search_input.textChanged.connect(self.on_text_changed)
        self.search_input.returnPressed.connect(self.on_return_pressed)
        self.search_input.setFont(QFont("Microsoft YaHei", 12))
        self.search_input.setMinimumHeight(40)
        
        # 后缀输入框
        self.suffix_input = QLineEdit()
        self.suffix_input.setPlaceholderText(".txt")
        self.suffix_input.textChanged.connect(self.on_suffix_changed)
        self.suffix_input.setFont(QFont("Microsoft YaHei", 10))
        self.suffix_input.setMinimumHeight(40)
        self.suffix_input.setMaximumWidth(80)
        
        # 设置焦点切换
        self.search_input.installEventFilter(self)
        self.suffix_input.installEventFilter(self)
        
        input_layout.addWidget(self.search_input, 7)
        input_layout.addWidget(self.suffix_input, 1)

        self.content_layout.addLayout(input_layout)
    
    def create_control_buttons(self):
        """创建控制按钮区域"""
        controls_layout = QHBoxLayout()
        controls_layout.setSpacing(8)
        
        # 操作方式按钮
        self.help_btn = QPushButton("操作方式")
        self.help_btn.setFixedSize(65, 28)
        self.help_btn.setToolTip("查看快捷键和操作说明")
        self.help_btn.clicked.connect(self.show_help_dialog)
        
        # 文件类型筛选按钮
        self.type_filter_btn = QPushButton("类型")
        self.type_filter_btn.setFixedSize(45, 28)
        self.type_filter_btn.setToolTip("文件类型筛选")
        self.type_filter_btn.clicked.connect(self.show_type_filter)
        
        # 路径管理按钮
        self.path_btn = QPushButton("目录")
        self.path_btn.setFixedSize(45, 28)
        self.path_btn.setToolTip("管理扫描目录")
        self.path_btn.clicked.connect(self.show_path_dialog)
        
        # 默认行为切换按钮
        self.action_btn = QPushButton()
        self.action_btn.setFixedSize(80, 28)
        self.action_btn.clicked.connect(self.toggle_action)
        self.update_action_button_text()
        
        # 布局按钮
        controls_layout.addStretch()
        controls_layout.addWidget(self.help_btn)
        controls_layout.addWidget(self.type_filter_btn)
        controls_layout.addWidget(self.path_btn)
        controls_layout.addWidget(self.action_btn)

        self.content_layout.addLayout(controls_layout)
    
    def create_results_list(self):
        """创建结果列表"""
        self.results_list = QListWidget()
        self.results_list.setMaximumHeight(350)
        self.results_list.setMinimumHeight(90)
        self.results_list.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.results_list.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.results_list.setVisible(False)
        self.results_list.itemActivated.connect(self.on_item_activated)

        self.content_layout.addWidget(self.results_list)
    
    def setup_window_properties(self):
        """设置窗口属性"""
        # 设置窗口标志 - 关键修改：确保窗口可以被其他应用覆盖
        self.setWindowFlags(
            Qt.WindowType.FramelessWindowHint |  # 无边框
            Qt.WindowType.Tool |                 # 工具窗口
            Qt.WindowType.WindowStaysOnTopHint   # 保持在顶部（但可以被其他应用覆盖）
        )

        # 设置窗口属性
        self.setAttribute(Qt.WidgetAttribute.WA_ShowWithoutActivating, False)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, True)  # 支持透明背景

        # 固定窗口大小 - 增加高度以容纳拖拽栏
        self.setFixedSize(500, 190)

        # 设置窗口样式
        self.setStyleSheet(self.get_window_style())

        # 添加阴影效果
        self.setup_shadow_effect()

        # 居中显示
        self.center_window()

    def setup_shadow_effect(self):
        """设置阴影效果"""
        try:
            from PySide6.QtWidgets import QGraphicsDropShadowEffect
            from PySide6.QtGui import QColor

            # 创建阴影效果
            shadow = QGraphicsDropShadowEffect()
            shadow.setBlurRadius(20)
            shadow.setXOffset(0)
            shadow.setYOffset(10)
            shadow.setColor(QColor(0, 0, 0, 80))  # 半透明黑色阴影

            # 应用阴影效果到主窗口
            self.setGraphicsEffect(shadow)

        except Exception as e:
            print(f"Failed to setup shadow effect: {e}")
    
    def setup_animations(self):
        """设置动画效果"""
        # 淡入淡出动画
        self.fade_animation = QPropertyAnimation(self, b"windowOpacity")
        self.fade_animation.setDuration(200)
        self.fade_animation.setEasingCurve(QEasingCurve.Type.OutCubic)

        # 缩放动画
        self.scale_animation = QPropertyAnimation(self, b"geometry")
        self.scale_animation.setDuration(200)
        self.scale_animation.setEasingCurve(QEasingCurve.Type.OutBack)
    
    def center_window(self):
        """将窗口居中显示在屏幕上方1/3处"""
        screen = QApplication.primaryScreen().geometry()
        x = (screen.width() - self.width()) // 2
        y = screen.height() // 3
        self.move(x, y)
    
    def show_window(self):
        """显示窗口并播放淡入动画"""
        # 获取目标位置
        screen = QApplication.primaryScreen().geometry()
        target_x = (screen.width() - self.width()) // 2
        target_y = screen.height() // 3

        # 设置初始状态（稍微小一点，从上方开始）
        self.setWindowOpacity(0.0)
        initial_size = 0.9
        initial_width = int(self.width() * initial_size)
        initial_height = int(self.height() * initial_size)
        initial_x = target_x + (self.width() - initial_width) // 2
        initial_y = target_y - 20  # 从稍微上方开始

        self.setGeometry(initial_x, initial_y, initial_width, initial_height)
        self.show()
        self.raise_()
        self.activateWindow()

        # 播放淡入动画
        self.fade_animation.setStartValue(0.0)
        self.fade_animation.setEndValue(1.0)
        self.fade_animation.start()

        # 播放缩放和位置动画
        from PySide6.QtCore import QRect
        target_rect = QRect(target_x, target_y, self.width(), self.height())
        self.scale_animation.setStartValue(self.geometry())
        self.scale_animation.setEndValue(target_rect)
        self.scale_animation.start()

        # 设置焦点到搜索框
        QTimer.singleShot(100, self._set_focus_to_search)

    def _set_focus_to_search(self):
        """设置焦点到搜索框"""
        self.search_input.setFocus()
        self.search_input.selectAll()

    def hide_window(self):
        """隐藏窗口并播放淡出动画"""
        # 播放淡出动画
        self.fade_animation.setStartValue(1.0)
        self.fade_animation.setEndValue(0.0)
        self.fade_animation.finished.connect(self._on_fade_out_finished)
        self.fade_animation.start()

    def _on_fade_out_finished(self):
        """淡出动画完成后隐藏窗口"""
        self.hide()
        self.window_hidden.emit()
        # 断开信号连接，避免重复调用
        self.fade_animation.finished.disconnect(self._on_fade_out_finished)

    def toggle_window(self):
        """切换窗口显示/隐藏状态"""
        if self.isVisible():
            self.hide_window()
        else:
            self.show_window()

    def on_tab_changed(self, index: int):
        """处理标签切换"""
        if index != self.current_profile_id:
            profile_manager.switch_profile(index)
    
    def on_profile_switched(self, profile_id: int):
        """处理Profile切换"""
        if profile_id == self.current_profile_id:
            return

        self.current_profile_id = profile_id

        # 更新标签栏
        self.tab_bar.setCurrentIndex(profile_id)

        # 更新搜索引擎
        self.search_engine = FileSearchEngine(profile_id=profile_id)
        self.search_worker.search_engine = self.search_engine

        # 清空搜索结果
        self.clear_results()

        # 根据输入状态决定显示内容
        if self.search_input.text().strip():
            self.trigger_search()
        else:
            # 如果没有输入，显示搜索范围信息
            self.show_search_scope_info()

        self.profile_changed.emit(profile_id)
    
    def on_text_changed(self, text: str):
        """处理文本输入变化"""
        self.trigger_search()
    
    def on_suffix_changed(self, text: str):
        """处理后缀输入变化"""
        self.trigger_search()

    def setup_gui_shortcuts(self):
        """设置GUI内热键（当窗口有焦点时工作）"""
        print("[GUI-SHORTCUT] 设置GUI内热键...")

        # 存储快捷键对象，防止被垃圾回收
        self.shortcuts = []

        # 为每个Profile创建快捷键 (Ctrl+0 到 Ctrl+9)
        for i in range(10):
            try:
                # 创建快捷键
                shortcut = QShortcut(QKeySequence(f"Ctrl+{i}"), self)

                # 使用lambda创建闭包，捕获当前的i值
                shortcut.activated.connect(lambda profile_id=i: self.switch_to_profile_via_shortcut(profile_id))

                # 保存快捷键引用
                self.shortcuts.append(shortcut)

                print(f"[GUI-SHORTCUT] Ctrl+{i} -> Profile {i} 已设置")

            except Exception as e:
                print(f"[ERROR] 设置Ctrl+{i}快捷键失败: {e}")

        print(f"[GUI-SHORTCUT] GUI内热键设置完成，共 {len(self.shortcuts)} 个快捷键")
        print("[GUI-SHORTCUT] 使用方法：当搜索窗口有焦点时，按 Ctrl+0~9 切换Profile")

    def switch_to_profile_via_shortcut(self, profile_id: int):
        """通过快捷键切换Profile"""
        import time
        current_time = time.strftime('%H:%M:%S', time.localtime())

        print(f"[GUI-SHORTCUT] 🎯 GUI热键触发: Ctrl+{profile_id} - 时间: {current_time}")
        print(f"[GUI-SHORTCUT] 当前Profile: {self.current_profile_id}")
        print(f"[GUI-SHORTCUT] 目标Profile: {profile_id}")

        if profile_id == self.current_profile_id:
            print(f"[GUI-SHORTCUT] 已经是Profile {profile_id}，无需切换")
            return

        try:
            print(f"[GUI-SHORTCUT] 执行Profile切换: {self.current_profile_id} -> {profile_id}")

            # 直接调用Profile管理器切换
            profile_manager.switch_profile(profile_id)

            print(f"[GUI-SHORTCUT] ✅ Profile切换成功: {profile_id}")

        except Exception as e:
            print(f"[ERROR] GUI热键Profile切换失败: {e}")
            import traceback
            traceback.print_exc()

    def trigger_search(self):
        """触发搜索"""
        self.search_timer.stop()

        main_query = self.search_input.text().strip()
        if main_query:
            # 现在总是执行搜索（无论是否配置了扫描目录）
            delay = config_manager.get_search_delay()
            self.search_timer.start(delay)
        else:
            # 没有输入时显示搜索范围信息
            self.clear_results()
            self.show_search_scope_info()
    
    def perform_search(self):
        """执行搜索"""
        query = self.search_input.text().strip()
        suffix_filter = self.suffix_input.text().strip()
        
        if query:
            search_query = query
            if suffix_filter:
                if not suffix_filter.startswith("."):
                    suffix_filter = f".{suffix_filter}"
                search_query = f"{query} ext:{suffix_filter[1:]}"
            
            self.search_worker.set_query(search_query)
            if not self.search_worker.isRunning():
                self.search_worker.start()
    
    def on_search_results(self, results: List[SearchResult]):
        """处理搜索结果"""
        self.clear_results()

        if not results:
            self.show_no_results_message()
            return

        # 应用文件类型过滤
        if self.enabled_file_types:
            filtered_results = self.filter_results_by_type(results)
            results = filtered_results

        # 对结果进行排序：快捷方式 > 文件夹 > 普通文件
        sorted_results = self.sort_search_results(results)

        # 显示结果
        for result in sorted_results:
            self.add_result_item(result)

        self.results_list.setVisible(True)
        self.adjust_window_size()

        # 选中第一项
        if self.results_list.count() > 0:
            self.results_list.setCurrentRow(0)

    def filter_results_by_type(self, results: List[SearchResult]) -> List[SearchResult]:
        """根据文件类型过滤结果"""
        if not self.enabled_file_types:
            return results

        filtered = []
        show_folders = "folders" in self.enabled_file_types

        # 获取文件扩展名
        file_categories = [cat for cat in self.enabled_file_types if cat != "folders"]
        allowed_extensions = []
        for category in file_categories:
            allowed_extensions.extend(self.search_engine.get_file_type_extensions(category))

        for result in results:
            if result.item_type == "folder":
                if show_folders:
                    filtered.append(result)
            elif result.item_type == "file":
                if file_categories:
                    if not result.suffix or result.suffix in allowed_extensions:
                        filtered.append(result)
                elif not show_folders:
                    filtered.append(result)

        return filtered

    def add_result_item(self, result: SearchResult):
        """添加结果项到列表"""
        item = QListWidgetItem()

        # 定义快速启动文件类型的图标映射
        quick_launch_icons = {
            ".lnk": "🔗",    # 快捷方式
            ".exe": "⚙️",    # 可执行程序
            ".url": "🌐",    # 网址快捷方式
            ".bat": "📜",    # 批处理文件
            ".cmd": "📜",    # 命令文件
            ".com": "⚙️",    # DOS可执行文件
            ".scr": "🖥️",    # 屏幕保护程序
            ".msi": "📦",    # Windows安装包
        }

        # 格式化显示信息
        if result.item_type == "folder":
            size_str = "文件夹"
            type_icon = "📁"
        elif result.suffix.lower() in quick_launch_icons:
            size_str = self.format_file_size(result.size)
            type_icon = quick_launch_icons[result.suffix.lower()]
        else:
            size_str = self.format_file_size(result.size)
            type_icon = "📄"

        display_name = result.filename
        item.setText(f"{type_icon} {display_name}\n{result.filepath} | {size_str}")

        # 存储完整结果数据
        item.setData(Qt.ItemDataRole.UserRole, result)

        self.results_list.addItem(item)

    def format_file_size(self, size: int) -> str:
        """格式化文件大小"""
        if size > 1024 * 1024:
            return f"{size / (1024 * 1024):.1f} MB"
        elif size > 1024:
            return f"{size / 1024:.1f} KB"
        else:
            return f"{size} bytes"

    def clear_results(self):
        """清空搜索结果"""
        self.results_list.clear()

    def sort_search_results(self, results: List[SearchResult]) -> List[SearchResult]:
        """对搜索结果进行排序：快速启动文件 > 文件夹 > 普通文件"""

        # 定义快速启动文件类型（可直接启动应用程序的文件）
        quick_launch_extensions = {
            ".lnk",    # 快捷方式
            ".exe",    # 可执行程序
            ".url",    # 网址快捷方式
            ".bat",    # 批处理文件
            ".cmd",    # 命令文件
            ".com",    # DOS可执行文件
            ".scr",    # 屏幕保护程序
            ".msi",    # Windows安装包
        }

        def get_sort_priority(result: SearchResult) -> int:
            """获取排序优先级，数字越小优先级越高"""
            if result.suffix.lower() in quick_launch_extensions:
                return 1  # 快速启动文件最高优先级
            elif result.item_type == "folder":
                return 2  # 文件夹第二优先级
            else:
                return 3  # 普通文件最低优先级

        # 按优先级排序，同优先级内按文件名排序
        return sorted(results, key=lambda x: (get_sort_priority(x), x.filename.lower()))

    def show_search_scope_info(self):
        """显示搜索范围信息"""
        from PySide6.QtWidgets import QListWidgetItem
        from PySide6.QtCore import Qt

        # 创建提示项
        item = QListWidgetItem()

        if self.search_engine.has_scan_directories():
            # 有配置扫描目录
            scan_dirs = self.search_engine.get_scan_directories()
            dir_count = len(scan_dirs)
            item.setText(f"📁 搜索范围：{dir_count}个指定目录\n\n点击'目录'按钮管理扫描目录")
        else:
            # 没有配置扫描目录，搜索整个系统
            item.setText("🌐 搜索范围：整个系统\n\n点击'目录'按钮添加指定目录以缩小搜索范围")

        item.setFlags(Qt.ItemFlag.NoItemFlags)  # 禁用选择
        item.setData(Qt.ItemDataRole.UserRole, "search_scope_info")

        self.results_list.addItem(item)
        self.results_list.setVisible(True)
        self.adjust_window_size()

    def show_no_results_message(self):
        """显示没有搜索结果的提示信息"""
        from PySide6.QtWidgets import QListWidgetItem
        from PySide6.QtCore import Qt

        # 创建提示项
        item = QListWidgetItem()

        # 根据搜索范围显示不同的提示
        if self.search_engine.has_scan_directories():
            # 在指定目录中搜索
            scan_info = self.search_engine.get_scan_directories_info()
            if scan_info["invalid_directories"]:
                # 有无效目录的情况
                item.setText("⚠️ 未找到搜索结果\n\n部分扫描目录不存在，请检查目录配置")
            else:
                # 正常情况，只是没有匹配的结果
                dir_count = len(scan_info["valid_directories"])
                item.setText(f"🔍 在{dir_count}个目录中未找到匹配文件\n\n尝试使用不同的关键词或检查文件类型筛选")
        else:
            # 在整个系统中搜索
            item.setText("🔍 在整个系统中未找到匹配文件\n\n尝试使用不同的关键词或检查文件类型筛选")

        item.setFlags(Qt.ItemFlag.NoItemFlags)  # 禁用选择
        item.setData(Qt.ItemDataRole.UserRole, "no_results_message")

        self.results_list.addItem(item)
        self.results_list.setVisible(True)
        self.adjust_window_size()

    def adjust_window_size(self):
        """根据结果数量调整窗口大小"""
        if self.results_list.isVisible() and self.results_list.count() > 0:
            base_height = 190  # 增加基础高度以容纳拖拽栏
            item_count = self.results_list.count()
            single_item_height = 72
            list_padding = 14

            visible_items = min(item_count, 5)
            list_height = visible_items * single_item_height + list_padding
            list_height = max(list_height, 90)

            new_height = base_height + list_height
            self.setFixedSize(500, new_height)
        else:
            self.setFixedSize(500, 190)  # 增加基础高度以容纳拖拽栏

        self.center_window()

    def on_return_pressed(self):
        """处理回车键按下"""
        if self.results_list.isVisible() and self.results_list.count() > 0:
            current_item = self.results_list.currentItem()
            if current_item:
                self.open_file(current_item)

    def on_item_activated(self, item: QListWidgetItem):
        """处理列表项激活"""
        self.open_file(item)

    def open_file(self, item: QListWidgetItem):
        """根据配置打开文件或目录"""
        result = item.data(Qt.ItemDataRole.UserRole)
        if result:
            file_path = result.filepath

            if result.item_type == "folder":
                # 文件夹直接打开
                if os.path.exists(file_path):
                    QDesktopServices.openUrl(QUrl.fromLocalFile(file_path))
                    print(f"打开文件夹: {file_path}")
                else:
                    print(f"文件夹不存在: {file_path}")
            else:
                # 文件根据当前设置打开
                if self.current_action == "open_file":
                    if os.path.exists(file_path):
                        QDesktopServices.openUrl(QUrl.fromLocalFile(file_path))
                        print(f"打开文件: {file_path}")
                    else:
                        print(f"文件不存在: {file_path}")
                else:
                    directory = os.path.dirname(file_path)
                    if os.path.exists(directory):
                        QDesktopServices.openUrl(QUrl.fromLocalFile(directory))
                        print(f"打开目录: {directory}")
                    else:
                        print(f"目录不存在: {directory}")

    def update_action_button_text(self):
        """更新行为按钮的文本显示"""
        if self.current_action == "open_file":
            self.action_btn.setText("打开文件")
            self.action_btn.setToolTip("当前：打开文件，点击或按Tab键切换为打开文件夹")
        else:
            self.action_btn.setText("打开文件夹")
            self.action_btn.setToolTip("当前：打开文件夹，点击或按Tab键切换为打开文件")

    def toggle_action(self):
        """切换默认行为"""
        if self.current_action == "open_file":
            self.current_action = "open_directory"
        else:
            self.current_action = "open_file"

        config_manager.set_default_action(self.current_action)
        self.update_action_button_text()
        print(f"默认行为已切换为: {self.current_action}")

    def show_help_dialog(self):
        """显示帮助对话框"""
        from .dialogs import HelpDialog
        dialog = HelpDialog(self)
        dialog.exec()

    def show_type_filter(self):
        """显示文件类型筛选对话框"""
        from .dialogs import FileTypeFilterDialog
        dialog = FileTypeFilterDialog(self)
        dialog.filter_changed.connect(self.on_file_type_filter_changed)
        dialog.exec()

    def show_path_dialog(self):
        """显示路径管理对话框"""
        from .dialogs import PathManagementDialog
        dialog = PathManagementDialog(self)
        dialog.path_added.connect(self.on_path_added)
        dialog.exec()

    def on_file_type_filter_changed(self, selected_types: List[str]):
        """文件类型筛选改变处理"""
        self.enabled_file_types = selected_types
        # 重新触发搜索以应用过滤
        if self.search_input.text().strip():
            self.trigger_search()

    def on_path_added(self, path: str):
        """路径添加/删除处理"""
        if path:
            print(f"已添加路径到Profile {self.current_profile_id}: {path}")
        else:
            print(f"Profile {self.current_profile_id} 的路径配置已更改")

        # 清空当前结果
        self.clear_results()

        # 如果有搜索内容，重新触发搜索
        if self.search_input.text().strip():
            self.trigger_search()
        else:
            # 如果没有搜索内容，显示搜索范围信息
            self.show_search_scope_info()

    def eventFilter(self, obj, event):
        """事件过滤器"""
        if event.type() == event.Type.KeyPress:
            if event.key() == Qt.Key.Key_Tab:
                self.toggle_action()
                return True
            elif event.key() == Qt.Key.Key_Right and obj == self.search_input:
                cursor_pos = self.search_input.cursorPosition()
                if cursor_pos == len(self.search_input.text()):
                    self.suffix_input.setFocus()
                    return True
            elif event.key() == Qt.Key.Key_Left and obj == self.suffix_input:
                cursor_pos = self.suffix_input.cursorPosition()
                if cursor_pos == 0:
                    self.search_input.setFocus()
                    return True

        return super().eventFilter(obj, event)

    def keyPressEvent(self, event):
        """处理键盘事件"""
        if event.key() == Qt.Key.Key_Escape:
            # ESC键: 退出整个应用程序
            if self.app_manager:
                print("[HOTKEY] ESC 检测到，退出应用程序")
                self.app_manager.quit_app()
            else:
                print("[HOTKEY] ESC 检测到，退出应用程序")
                QApplication.instance().quit()
        elif event.key() == Qt.Key.Key_Tab:
            if self.isVisible():
                self.toggle_action()
            else:
                super().keyPressEvent(event)
        elif event.key() == Qt.Key.Key_Down:
            if self.results_list.isVisible():
                if self.results_list.hasFocus():
                    super().keyPressEvent(event)
                else:
                    self.results_list.setFocus()
                    self.results_list.setCurrentRow(0)
        elif event.key() == Qt.Key.Key_Up:
            if self.results_list.isVisible() and self.results_list.hasFocus():
                if self.results_list.currentRow() == 0:
                    self.search_input.setFocus()
                else:
                    super().keyPressEvent(event)
        else:
            super().keyPressEvent(event)

    def get_window_style(self) -> str:
        """获取窗口样式"""
        return """
        FloatingSearchWindow {
            background-color: rgba(255, 255, 255, 0.96);
            border: 1px solid rgba(220, 220, 220, 0.6);
            border-radius: 8px;
            font-family: 'Microsoft YaHei';
        }

        QWidget:not(DragHandle) {
            background-color: transparent;
            font-family: 'Microsoft YaHei';
        }

        QLineEdit {
            background-color: rgba(255, 255, 255, 0.92);
            border: 2px solid rgba(224, 224, 224, 0.7);
            border-radius: 6px;
            padding: 8px 12px;
            font-size: 12px;
            color: #2c3e50;
            selection-background-color: rgba(74, 144, 226, 0.3);
            font-weight: 500;
        }

        QLineEdit:focus {
            border-color: rgba(74, 144, 226, 0.9);
            background-color: rgba(255, 255, 255, 0.98);
        }

        QLineEdit:hover {
            background-color: rgba(255, 255, 255, 0.95);
            border-color: rgba(74, 144, 226, 0.5);
        }

        QPushButton {
            background-color: rgba(248, 249, 250, 0.9);
            border: 1px solid rgba(222, 226, 230, 0.7);
            border-radius: 4px;
            padding: 6px 12px;
            font-size: 11px;
            color: #34495e;
            font-weight: 600;
        }

        QPushButton:hover {
            background-color: rgba(233, 236, 239, 0.95);
            border-color: rgba(173, 181, 189, 0.9);
            color: #2c3e50;
        }

        QPushButton:pressed {
            background-color: rgba(222, 226, 230, 0.95);
        }

        QListWidget {
            background-color: rgba(255, 255, 255, 0.94);
            border: 1px solid rgba(224, 224, 224, 0.6);
            border-radius: 6px;
            padding: 6px;
            font-size: 11px;
            color: #2c3e50;
        }

        QListWidget::item {
            background-color: transparent;
            border: none;
            padding: 12px;
            border-radius: 4px;
            margin: 3px 0px;
            min-height: 48px;
            color: #34495e;
        }

        QListWidget::item:selected {
            background-color: rgba(74, 144, 226, 0.15);
            border: 1px solid rgba(74, 144, 226, 0.3);
            color: #2c3e50;
        }

        QListWidget::item:hover {
            background-color: rgba(74, 144, 226, 0.08);
        }

        QListWidget::item:selected:hover {
            background-color: rgba(74, 144, 226, 0.22);
        }

        QScrollBar:vertical {
            background-color: rgba(240, 240, 240, 0.6);
            width: 8px;
            border-radius: 4px;
        }

        QScrollBar::handle:vertical {
            background-color: rgba(160, 160, 160, 0.8);
            border-radius: 4px;
            min-height: 20px;
        }

        QScrollBar::handle:vertical:hover {
            background-color: rgba(120, 120, 120, 0.9);
        }

        QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
            height: 0px;
        }
        """

    def get_tab_style(self) -> str:
        """获取标签栏样式"""
        return """
        QTabBar::tab {
            background: rgba(240, 240, 240, 0.8);
            border: 1px solid #ccc;
            padding: 4px 12px;
            margin-right: 2px;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
            min-width: 30px;
            font-size: 11px;
            color: #666666;
        }

        QTabBar::tab:selected {
            background: rgba(255, 255, 255, 0.95);
            border-bottom-color: rgba(255, 255, 255, 0.95);
            font-weight: bold;
            color: #333333;
        }

        QTabBar::tab:hover {
            background: rgba(232, 232, 232, 0.9);
        }
        """

    def closeEvent(self, event):
        """窗口关闭事件"""
        # 停止搜索线程
        if self.search_worker.isRunning():
            self.search_worker.quit()
            self.search_worker.wait()

        # 移除Profile切换回调
        profile_manager.remove_profile_switch_callback(self.on_profile_switched)

        event.accept()
