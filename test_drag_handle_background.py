#!/usr/bin/env python3
"""
测试拖拽手柄背景显示
"""

import sys
from PySide6.QtWidgets import QApplication
from simple_desktop.ui.search_window import FloatingSearchWindow

def main():
    """测试拖拽手柄背景"""
    print("🎨 测试拖拽手柄背景显示")
    
    app = QApplication(sys.argv)
    
    # 创建搜索窗口
    search_window = FloatingSearchWindow()
    
    print("✅ 搜索窗口已创建")
    print("📝 背景测试要点:")
    print("   - 拖拽手柄应该有明显的白色背景 rgba(255, 255, 255, 0.96)")
    print("   - 拖拽手柄应该是圆角矩形形状")
    print("   - 拖拽手柄从左边缘延伸到关闭按钮左侧，保持10px间距")
    print("   - 拖拽手柄内应该显示 '≡' 图标和 '拖拽移动窗口' 文字")
    print("   - 关闭按钮也应该有相同的白色背景")
    print("   - 悬停时背景应该稍微变亮")
    
    # 显示窗口
    search_window.show_window()
    
    print("\n🎯 测试窗口已启动！")
    print("请检查拖拽手柄的白色背景是否正确显示")
    print("按ESC键或点击关闭按钮退出")
    
    # 运行应用
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
