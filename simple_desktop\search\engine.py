"""
基于Everything SDK的搜索引擎
"""

import os
import re
from typing import List, Optional, Dict, Any
from dataclasses import dataclass

from ..core.everything_sdk import get_everything_sdk, SearchResult as EverythingResult


@dataclass
class SearchResult:
    """统一的搜索结果数据类"""
    filename: str
    filepath: str
    item_type: str  # "file" or "folder"
    size: int = 0
    suffix: str = ""
    date_modified: Optional[str] = None
    
    @classmethod
    def from_everything_result(cls, result: EverythingResult) -> 'SearchResult':
        """从Everything结果创建SearchResult"""
        # 修复：确保suffix包含点号
        suffix = result.extension
        if suffix and not suffix.startswith("."):
            suffix = f".{suffix}"

        return cls(
            filename=result.filename,
            filepath=result.full_path,
            item_type="folder" if result.is_folder else "file",
            size=result.size,
            suffix=suffix,
            date_modified=result.date_modified
        )


class FileSearchEngine:
    """文件搜索引擎"""
    
    def __init__(self, profile_id: int = 0):
        """
        初始化搜索引擎
        
        Args:
            profile_id: Profile ID (0-9)
        """
        self.profile_id = profile_id
        self.everything_sdk = get_everything_sdk()
        
        # 文件类型映射
        self.file_type_extensions = {
            "documents": [".txt", ".doc", ".docx", ".pdf", ".rtf", ".odt", ".xls", ".xlsx", ".ppt", ".pptx"],
            "images": [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff", ".svg", ".ico", ".webp"],
            "videos": [".mp4", ".avi", ".mkv", ".mov", ".wmv", ".flv", ".webm", ".m4v", ".3gp"],
            "audio": [".mp3", ".wav", ".flac", ".aac", ".ogg", ".wma", ".m4a"],
            "archives": [".zip", ".rar", ".7z", ".tar", ".gz", ".bz2", ".xz"],
            "executables": [".exe", ".msi", ".bat", ".cmd", ".com", ".scr", ".lnk", ".url"],
            "code": [".py", ".js", ".html", ".css", ".cpp", ".c", ".java", ".cs", ".php", ".rb", ".go"]
        }
    
    def search(self, query: str, limit: int = 50, file_types: Optional[List[str]] = None,
               include_folders: bool = True) -> List[SearchResult]:
        """
        执行搜索

        Args:
            query: 搜索查询字符串
            limit: 最大结果数量
            file_types: 文件类型过滤列表
            include_folders: 是否包含文件夹

        Returns:
            搜索结果列表
        """
        if not query.strip():
            return []

        # 获取当前Profile的扫描目录
        from ..core.profile_manager import profile_manager
        scan_directories = profile_manager.get_profile_scan_directories(self.profile_id)

        try:
            # 根据是否配置了扫描目录选择搜索策略
            if scan_directories:
                use_directory_filter = True
            else:
                use_directory_filter = False
                scan_directories = []

            # 处理文件类型过滤的特殊情况
            if file_types and any(ft != "folders" for ft in file_types):
                # 有文件类型过滤，需要为每个扩展名单独搜索
                results = self._search_with_file_types(
                    query, scan_directories, file_types, include_folders,
                    limit, use_directory_filter
                )
            else:
                # 没有文件类型过滤或只有文件夹过滤，使用标准搜索
                if scan_directories:
                    everything_query = self._build_everything_query_with_directories(
                        query, scan_directories, file_types, include_folders
                    )
                else:
                    everything_query = self._build_everything_query(query, file_types, include_folders)

                # 执行Everything搜索
                search_limit = limit * 10 if use_directory_filter else limit * 2
                everything_results = self.everything_sdk.search(
                    query=everything_query,
                    max_results=search_limit,
                    match_case=False,
                    match_whole_word=False,
                    use_regex=False
                )

                # 转换结果格式
                results = self._process_search_results(
                    everything_results, scan_directories, file_types,
                    include_folders, use_directory_filter, limit
                )

            return results

        except Exception as e:
            print(f"Search error: {e}")
            return []

    def _search_with_file_types(self, query: str, scan_directories: List[str],
                               file_types: List[str], include_folders: bool,
                               limit: int, use_directory_filter: bool) -> List[SearchResult]:
        """为每个文件类型单独搜索并合并结果"""
        all_results = []
        seen_paths = set()

        # 获取所有需要搜索的扩展名
        file_extensions = []
        for file_type in file_types:
            if file_type != "folders" and file_type in self.file_type_extensions:
                file_extensions.extend(self.file_type_extensions[file_type])

        # 为每个扩展名单独搜索
        for ext in file_extensions:
            ext_name = ext[1:]  # 去掉点号

            # 构建查询
            if scan_directories:
                # 使用简单查询 + 后续路径过滤
                ext_query = f"{query} ext:{ext_name}"
            else:
                ext_query = f"{query} ext:{ext_name}"

            try:
                # 执行搜索
                search_limit = limit * 2
                everything_results = self.everything_sdk.search(
                    query=ext_query,
                    max_results=search_limit,
                    match_case=False,
                    match_whole_word=False,
                    use_regex=False
                )

                # 处理结果
                for result in everything_results:
                    # 避免重复结果
                    if result.full_path in seen_paths:
                        continue
                    seen_paths.add(result.full_path)

                    search_result = SearchResult.from_everything_result(result)

                    # 应用目录过滤
                    should_include_by_directory = True
                    if use_directory_filter:
                        should_include_by_directory = self._is_in_scan_directories(
                            search_result.filepath, scan_directories
                        )

                    if should_include_by_directory:
                        # 应用文件类型过滤
                        should_include_by_type = self._should_include_result(
                            search_result, file_types, include_folders
                        )

                        if should_include_by_type:
                            all_results.append(search_result)

                        # 限制结果数量
                        if len(all_results) >= limit:
                            break

                # 如果已经达到限制，停止搜索其他扩展名
                if len(all_results) >= limit:
                    break

            except Exception as e:
                print(f"Error searching for extension {ext}: {e}")
                continue

        return all_results

    def _process_search_results(self, everything_results, scan_directories: List[str],
                               file_types: Optional[List[str]], include_folders: bool,
                               use_directory_filter: bool, limit: int) -> List[SearchResult]:
        """处理搜索结果"""
        results = []
        for result in everything_results:
            search_result = SearchResult.from_everything_result(result)

            # 根据搜索策略决定是否需要目录过滤
            should_include_by_directory = True
            if use_directory_filter:
                should_include_by_directory = self._is_in_scan_directories(
                    search_result.filepath, scan_directories
                )

            if should_include_by_directory:
                # 应用文件类型过滤
                should_include_by_type = self._should_include_result(
                    search_result, file_types, include_folders
                )

                if should_include_by_type:
                    results.append(search_result)

                # 限制结果数量
                if len(results) >= limit:
                    break

        return results
    
    def _build_everything_query_with_directories(self, query: str, scan_directories: List[str],
                                                file_types: Optional[List[str]] = None,
                                                include_folders: bool = True) -> str:
        """构建包含目录限制的Everything查询字符串"""
        # 基础查询
        base_query = query

        # 处理后缀过滤
        if "suffix:" in query or "ext:" in query:
            # 查询中已包含后缀过滤，直接使用目录限制
            directory_filters = []
            for directory in scan_directories:
                normalized_path = os.path.normpath(directory)
                if not normalized_path.endswith("\\"):
                    normalized_path += "\\"
                directory_filters.append(f'"{normalized_path}"')

            if len(directory_filters) == 1:
                directory_query = directory_filters[0]
            else:
                directory_query = "(" + " | ".join(directory_filters) + ")"

            return f"{directory_query} {base_query}"

        # 构建查询组件
        query_parts = []

        # 处理文件类型过滤
        if file_types:
            # 有指定文件类型过滤

            # 处理文件夹
            if "folders" in file_types and include_folders:
                # 为每个目录构建folder查询
                folder_queries = []
                for directory in scan_directories:
                    normalized_path = os.path.normpath(directory)
                    if not normalized_path.endswith("\\"):
                        normalized_path += "\\"
                    folder_queries.append(f'"{normalized_path}" folder:{base_query}')

                if folder_queries:
                    # 修复：不要在OR查询外面加括号
                    query_parts.append(" | ".join(folder_queries))

            # 处理文件类型
            file_extensions = []
            for file_type in file_types:
                if file_type != "folders" and file_type in self.file_type_extensions:
                    file_extensions.extend(self.file_type_extensions[file_type])

            if file_extensions:
                # 修复：Everything不支持复杂的扩展名OR语法
                # 为每个扩展名单独构建查询，然后在应用层合并结果
                # 这里只使用第一个扩展名，其他扩展名的结果将通过多次搜索获得
                first_ext = file_extensions[0][1:]  # 去掉点号
                file_query = f"{base_query} ext:{first_ext}"
                query_parts.append(file_query)

            # 组合所有查询部分
            if query_parts:
                if len(query_parts) == 1:
                    return query_parts[0]
                else:
                    # 修复：不要在最外层加括号
                    return " | ".join(query_parts)
            else:
                # 没有匹配的类型，返回空查询
                return f'"{scan_directories[0]}\\" nonexistent_placeholder_query'
        else:
            # 没有指定文件类型过滤，搜索所有内容（包括文件夹）
            # 优化：使用简单的查询语法，路径过滤在后续处理
            return base_query

    def _build_everything_query(self, query: str, file_types: Optional[List[str]] = None,
                               include_folders: bool = True) -> str:
        """构建Everything查询字符串（保留原方法以兼容性）"""
        # 基础查询
        everything_query = query

        # 处理后缀过滤
        if "suffix:" in query:
            # 查询中已包含后缀过滤，直接使用
            return everything_query

        # 添加文件类型过滤
        if file_types:
            type_filters = []

            # 处理文件夹
            if "folders" in file_types and include_folders:
                type_filters.append("folder:")

            # 处理文件类型
            file_extensions = []
            for file_type in file_types:
                if file_type != "folders" and file_type in self.file_type_extensions:
                    file_extensions.extend(self.file_type_extensions[file_type])

            if file_extensions:
                # 构建扩展名过滤
                ext_filter = " | ".join([f"ext:{ext[1:]}" for ext in file_extensions])
                if ext_filter:
                    type_filters.append(f"({ext_filter})")

            if type_filters:
                everything_query += " " + " | ".join(type_filters)

        return everything_query

    def _is_in_scan_directories(self, filepath: str, scan_directories: List[str]) -> bool:
        """检查文件路径是否在配置的扫描目录中"""
        if not filepath or not scan_directories:
            return False

        # 规范化文件路径
        normalized_filepath = os.path.normpath(filepath).lower()

        # 检查是否在任何一个扫描目录中
        for directory in scan_directories:
            normalized_directory = os.path.normpath(directory).lower()

            # 确保目录路径以分隔符结尾
            if not normalized_directory.endswith(os.sep):
                normalized_directory += os.sep

            # 检查文件路径是否以目录路径开头
            if normalized_filepath.startswith(normalized_directory):
                return True

            # 也检查完全匹配的情况（文件就在目录根目录下）
            if os.path.dirname(normalized_filepath + os.sep) == normalized_directory.rstrip(os.sep):
                return True

        return False
    
    def _should_include_result(self, result: SearchResult, file_types: Optional[List[str]] = None,
                              include_folders: bool = True) -> bool:
        """判断是否应该包含此结果"""
        # 检查文件夹
        if result.item_type == "folder":
            if not include_folders:
                return False

            # 如果没有文件类型过滤，包含所有文件夹
            if not file_types:
                return True

            # 如果有文件类型过滤，只有明确包含"folders"时才包含文件夹
            return "folders" in file_types

        # 检查文件类型
        if file_types:
            # 有文件类型过滤
            result_ext = result.suffix.lower()

            # 检查是否匹配任何选中的文件类型
            for file_type in file_types:
                if file_type != "folders" and file_type in self.file_type_extensions:
                    if result_ext in self.file_type_extensions[file_type]:
                        return True

            # 如果没有匹配任何文件类型，返回False
            return False

        # 没有文件类型过滤，包含所有文件
        return True
    
    def search_with_suffix(self, query: str, suffix: str, limit: int = 50) -> List[SearchResult]:
        """
        带后缀过滤的搜索

        Args:
            query: 搜索查询字符串
            suffix: 文件后缀（如 .txt）
            limit: 最大结果数量

        Returns:
            搜索结果列表
        """
        if not suffix.startswith("."):
            suffix = f".{suffix}"

        # 构建带后缀的查询
        full_query = f"{query} ext:{suffix[1:]}"

        return self.search(full_query, limit=limit, include_folders=False)

    def search_in_directory(self, query: str, directory: str, limit: int = 50) -> List[SearchResult]:
        """
        在指定目录中搜索（已更新为使用配置的扫描目录）

        Args:
            query: 搜索查询字符串
            directory: 目录路径（此参数现在被忽略，使用Profile配置的目录）
            limit: 最大结果数量

        Returns:
            搜索结果列表
        """
        # 注意：此方法现在使用Profile配置的扫描目录，而不是传入的directory参数
        print("注意：search_in_directory现在使用Profile配置的扫描目录")
        return self.search(query, limit=limit)
    
    def get_file_type_extensions(self, file_type: str) -> List[str]:
        """获取文件类型对应的扩展名列表"""
        return self.file_type_extensions.get(file_type, [])
    
    def get_all_file_types(self) -> List[str]:
        """获取所有支持的文件类型"""
        return list(self.file_type_extensions.keys()) + ["folders"]

    def has_scan_directories(self) -> bool:
        """检查当前Profile是否配置了扫描目录"""
        from ..core.profile_manager import profile_manager
        scan_directories = profile_manager.get_profile_scan_directories(self.profile_id)
        return len(scan_directories) > 0

    def get_scan_directories(self) -> List[str]:
        """获取当前Profile的扫描目录列表"""
        from ..core.profile_manager import profile_manager
        return profile_manager.get_profile_scan_directories(self.profile_id)

    def get_scan_directories_info(self) -> Dict[str, Any]:
        """获取扫描目录的详细信息"""
        scan_directories = self.get_scan_directories()
        info = {
            "count": len(scan_directories),
            "directories": scan_directories,
            "valid_directories": [],
            "invalid_directories": []
        }

        for directory in scan_directories:
            if os.path.exists(directory) and os.path.isdir(directory):
                info["valid_directories"].append(directory)
            else:
                info["invalid_directories"].append(directory)

        return info

    def is_everything_available(self) -> bool:
        """检查Everything是否可用"""
        return self.everything_sdk.is_everything_running()

    def get_everything_version(self) -> Dict[str, Any]:
        """获取Everything版本信息"""
        return self.everything_sdk.get_version_info()

    def search_in_directory(self, query: str, directory: str, limit: int = 50) -> List[SearchResult]:
        """
        在指定目录中搜索

        Args:
            query: 搜索查询字符串
            directory: 目录路径
            limit: 最大结果数量

        Returns:
            搜索结果列表
        """
        # 构建目录限制的查询
        dir_query = f'"{directory}" {query}'
        return self.search(dir_query, limit=limit)

    def search_by_extension(self, extension: str, limit: int = 50) -> List[SearchResult]:
        """
        按扩展名搜索

        Args:
            extension: 文件扩展名（如 .txt）
            limit: 最大结果数量

        Returns:
            搜索结果列表
        """
        if not extension.startswith("."):
            extension = f".{extension}"

        query = f"ext:{extension[1:]}"
        return self.search(query, limit=limit, include_folders=False)

    def search_recent_files(self, days: int = 7, limit: int = 50) -> List[SearchResult]:
        """
        搜索最近修改的文件

        Args:
            days: 天数
            limit: 最大结果数量

        Returns:
            搜索结果列表
        """
        query = f"dm:last{days}days"
        return self.search(query, limit=limit, include_folders=False)

    def search_large_files(self, min_size_mb: int = 100, limit: int = 50) -> List[SearchResult]:
        """
        搜索大文件

        Args:
            min_size_mb: 最小文件大小（MB）
            limit: 最大结果数量

        Returns:
            搜索结果列表
        """
        query = f"size:>{min_size_mb}mb"
        return self.search(query, limit=limit, include_folders=False)
